<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />

<div class="swiper-container">
  <div class="swiper-wrapper">
    <div class="swiper-slide"><img src="image1.jpg" alt="Image 1"></div>
    <div class="swiper-slide"><img src="image2.jpg" alt="Image 2"></div>
    <div class="swiper-slide"><img src="image3.jpg" alt="Image 3"></div>
    <!-- Ajoutez d'autres slides selon besoin -->
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
<script>
  const swiper = new Swiper('.swiper-container', {
    effect: 'coverflow',
    grabCursor: true,
    centeredSlides: true,
    slidesPerView: 'auto',
    coverflowEffect: {
      rotate: 0,
      stretch: 0,
      depth: 100,
      modifier: 1,
      slideShadows: true,
    }
  });
</script>