<?php
/*
Plugin Name: WooCommerce Order Status Role Filter
Description: Permet de définir quels statuts de commande sont visibles et modifiables selon le rôle utilisateur.
Version: 2.0
Author: Votre Nom
*/

// Sécurité : empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

class WooOrderStatusRoleFilter {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_migrate_order_rules', array($this, 'handle_migration_ajax'));
        
        // Hooks pour les shortcodes ou pages personnalisées
        add_shortcode('woo_user_orders', array($this, 'display_user_orders_shortcode'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Filtrage des commandes', 
            'Filtrage commandes', 
            'manage_options', 
            'woo-order-filter', 
            array($this, 'settings_page'),
            'dashicons-filter'
        );
        
        add_submenu_page(
            'woo-order-filter',
            'Migration des règles',
            'Migration',
            'manage_options',
            'woo-order-migration',
            array($this, 'migration_page')
        );
    }
    
    public function settings_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.'));
        }

        // 💾 Sauvegarder les règles
        if (isset($_POST['submit_rules']) && wp_verify_nonce($_POST['order_rules_nonce'], 'save_order_rules')) {
            $this->save_rules();
        }

        // 🗑️ Supprimer une règle
        if (isset($_GET['delete_role']) && wp_verify_nonce($_GET['_wpnonce'], 'delete_rule')) {
            $this->delete_rule(sanitize_text_field($_GET['delete_role']));
        }

        $this->render_settings_page();
    }
    
    private function save_rules() {
        $rules = [];
        
        if (isset($_POST['roles']) && is_array($_POST['roles'])) {
            foreach ($_POST['roles'] as $role => $data) {
                $rules[sanitize_text_field($role)] = [
                    'view' => isset($data['view']) ? array_map('sanitize_text_field', $data['view']) : [],
                    'change' => isset($data['change']) ? array_map('sanitize_text_field', $data['change']) : []
                ];
            }
        }
        
        update_option('woo_order_filter_rules_v2', $rules);
        add_settings_error('woo_order_filter', 'rules_saved', 'Règles sauvegardées avec succès !', 'updated');
    }
    
    private function delete_rule($role) {
        $rules = get_option('woo_order_filter_rules_v2', []);
        if (isset($rules[$role])) {
            unset($rules[$role]);
            update_option('woo_order_filter_rules_v2', $rules);
            add_settings_error('woo_order_filter', 'rule_deleted', 'Règle supprimée avec succès !', 'updated');
        }
    }
    
    private function render_settings_page() {
        $current_rules = get_option('woo_order_filter_rules_v2', []);
        $all_roles = wp_roles()->get_names();
        $order_statuses = wc_get_order_statuses();
        
        settings_errors('woo_order_filter');
        ?>
        <div class="wrap">
            <h1><span class="dashicons dashicons-filter"></span> Configuration des Statuts de Commandes par Rôle</h1>
            
            <div class="notice notice-info">
                <p><strong>Nouveauté v2.0 :</strong> Vous pouvez maintenant définir séparément les statuts <strong>visibles</strong> et <strong>modifiables</strong> pour chaque rôle.</p>
            </div>
            
            <form method="post" action="">
                <?php wp_nonce_field('save_order_rules', 'order_rules_nonce'); ?>
                
                <div class="postbox-container" style="width: 100%;">
                    <div class="meta-box-sortables">
                        
                        <?php foreach ($all_roles as $role_key => $role_name): ?>
                        <div class="postbox">
                            <h2 class="hndle">
                                <span><?php echo esc_html($role_name); ?> <code>(<?php echo esc_html($role_key); ?>)</code></span>
                                <?php if (isset($current_rules[$role_key])): ?>
                                    <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=woo-order-filter&delete_role=' . urlencode($role_key)), 'delete_rule'); ?>" 
                                       class="button button-small button-link-delete" 
                                       style="float: right; margin-top: 5px;"
                                       onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette règle ?')">
                                        Supprimer
                                    </a>
                                <?php endif; ?>
                            </h2>
                            <div class="inside">
                                <table class="form-table">
                                    <tr>
                                        <th scope="row" style="width: 200px; vertical-align: top; padding-top: 15px;">
                                            <label><strong>👁️ Statuts visibles</strong></label>
                                            <p class="description">Les statuts que ce rôle peut voir dans la liste des commandes</p>
                                        </th>
                                        <td>
                                            <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #fafafa;">
                                                <?php foreach ($order_statuses as $status_key => $status_name): ?>
                                                    <?php 
                                                    $view_checked = isset($current_rules[$role_key]['view']) && 
                                                                  in_array($status_key, $current_rules[$role_key]['view']);
                                                    ?>
                                                    <label style="display: block; margin-bottom: 8px;">
                                                        <input type="checkbox" 
                                                               name="roles[<?php echo esc_attr($role_key); ?>][view][]" 
                                                               value="<?php echo esc_attr($status_key); ?>"
                                                               <?php checked($view_checked); ?>>
                                                        <span class="wc-order-status status-<?php echo esc_attr(str_replace('wc-', '', $status_key)); ?>">
                                                            <?php echo esc_html($status_name); ?>
                                                        </span>
                                                    </label>
                                                <?php endforeach; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <th scope="row" style="vertical-align: top; padding-top: 15px;">
                                            <label><strong>✏️ Statuts modifiables</strong></label>
                                            <p class="description">Les statuts que ce rôle peut assigner ou changer</p>
                                        </th>
                                        <td>
                                            <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #fff8dc;">
                                                <?php foreach ($order_statuses as $status_key => $status_name): ?>
                                                    <?php 
                                                    $change_checked = isset($current_rules[$role_key]['change']) && 
                                                                    in_array($status_key, $current_rules[$role_key]['change']);
                                                    ?>
                                                    <label style="display: block; margin-bottom: 8px;">
                                                        <input type="checkbox" 
                                                               name="roles[<?php echo esc_attr($role_key); ?>][change][]" 
                                                               value="<?php echo esc_attr($status_key); ?>"
                                                               <?php checked($change_checked); ?>>
                                                        <span class="wc-order-status status-<?php echo esc_attr(str_replace('wc-', '', $status_key)); ?>">
                                                            <?php echo esc_html($status_name); ?>
                                                        </span>
                                                    </label>
                                                <?php endforeach; ?>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        
                    </div>
                </div>
                
                <p class="submit">
                    <input type="submit" name="submit_rules" class="button-primary" value="💾 Sauvegarder toutes les règles">
                </p>
            </form>
            
            <!-- 📊 Tableau récapitulatif -->
            <?php $this->render_summary_table($current_rules, $all_roles, $order_statuses); ?>
            
        </div>
        
        <style>
        .wc-order-status {
            display: inline-block;
            padding: 2px 8px;
            margin-left: 5px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
        }
        .status-pending { background-color: #ffba00; }
        .status-processing { background-color: #c6e1c6; color: #5b841b; }
        .status-on-hold { background-color: #f8dda7; color: #94660c; }
        .status-completed { background-color: #c8d7e1; color: #2e4453; }
        .status-cancelled { background-color: #eba3a3; color: #761919; }
        .status-refunded { background-color: #eba3a3; color: #761919; }
        .status-failed { background-color: #eba3a3; color: #761919; }
        
        .postbox { margin-bottom: 20px; }
        .form-table .description { font-style: italic; color: #666; font-size: 12px; margin-top: 5px; }
        </style>
        <?php
    }
    
    private function render_summary_table($current_rules, $all_roles, $order_statuses) {
        if (empty($current_rules)) return;
        ?>
        <div class="postbox">
            <h2 class="hndle"><span>📊 Récapitulatif des règles configurées</span></h2>
            <div class="inside">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th style="width: 20%;">Rôle</th>
                            <th style="width: 40%;">👁️ Statuts Visibles</th>
                            <th style="width: 40%;">✏️ Statuts Modifiables</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($current_rules as $role => $permissions): ?>
                        <tr>
                            <td><strong><?php echo esc_html($all_roles[$role] ?? $role); ?></strong></td>
                            <td>
                                <?php if (empty($permissions['view'])): ?>
                                    <em style="color: #999;">Aucun</em>
                                <?php else: ?>
                                    <?php foreach ($permissions['view'] as $status): ?>
                                        <span class="wc-order-status status-<?php echo esc_attr(str_replace('wc-', '', $status)); ?>">
                                            <?php echo esc_html($order_statuses[$status] ?? $status); ?>
                                        </span>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (empty($permissions['change'])): ?>
                                    <em style="color: #999;">Aucun</em>
                                <?php else: ?>
                                    <?php foreach ($permissions['change'] as $status): ?>
                                        <span class="wc-order-status status-<?php echo esc_attr(str_replace('wc-', '', $status)); ?>">
                                            <?php echo esc_html($order_statuses[$status] ?? $status); ?>
                                        </span>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php
    }
    
    public function migration_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Vous n\'avez pas les permissions nécessaires.'));
        }
        
        // Lancer la migration si demandée
        if (isset($_POST['run_migration']) && wp_verify_nonce($_POST['migration_nonce'], 'run_migration')) {
            $this->migrate_old_rules();
        }
        
        $old_rules = get_option('woo_order_filter_rules', []);
        $new_rules = get_option('woo_order_filter_rules_v2', []);
        ?>
        <div class="wrap">
            <h1>🔄 Migration des Règles</h1>
            
            <?php if (empty($old_rules) && empty($new_rules)): ?>
                <div class="notice notice-info">
                    <p>Aucune règle configurée dans l'ancien ou le nouveau format.</p>
                </div>
            <?php elseif (!empty($old_rules) && empty($new_rules)): ?>
                <div class="notice notice-warning">
                    <p><strong>Migration nécessaire !</strong> Des règles existent dans l'ancien format.</p>
                </div>
                <form method="post">
                    <?php wp_nonce_field('run_migration', 'migration_nonce'); ?>
                    <p>
                        <input type="submit" name="run_migration" class="button button-primary" 
                               value="🚀 Lancer la migration"
                               onclick="return confirm('Voulez-vous migrer les anciennes règles vers le nouveau format ?')">
                    </p>
                </form>
            <?php elseif (empty($old_rules) && !empty($new_rules)): ?>
                <div class="notice notice-success">
                    <p>✅ Système à jour ! Utilisation du nouveau format.</p>
                </div>
            <?php else: ?>
                <div class="notice notice-info">
                    <p>Les deux formats coexistent. Migration disponible pour synchroniser.</p>
                </div>
                <form method="post">
                    <?php wp_nonce_field('run_migration', 'migration_nonce'); ?>
                    <p>
                        <input type="submit" name="run_migration" class="button button-secondary" 
                               value="🔄 Re-migrer (remplacer)">
                    </p>
                </form>
            <?php endif; ?>
            
            <?php if (!empty($old_rules)): ?>
                <h2>📋 Anciennes règles (v1)</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr><th>Rôle</th><th>Statuts</th></tr>
                    </thead>
                    <tbody>
                        <?php foreach ($old_rules as $role => $statuses): ?>
                        <tr>
                            <td><?php echo esc_html($role); ?></td>
                            <td><?php echo esc_html(implode(', ', $statuses)); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
            
            <?php if (!empty($new_rules)): ?>
                <h2>🆕 Nouvelles règles (v2)</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr><th>Rôle</th><th>Visibles</th><th>Modifiables</th></tr>
                    </thead>
                    <tbody>
                        <?php foreach ($new_rules as $role => $permissions): ?>
                        <tr>
                            <td><?php echo esc_html($role); ?></td>
                            <td><?php echo esc_html(implode(', ', $permissions['view'] ?? [])); ?></td>
                            <td><?php echo esc_html(implode(', ', $permissions['change'] ?? [])); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
        <?php
    }
    
    private function migrate_old_rules() {
        $old_rules = get_option('woo_order_filter_rules', []);
        
        if (empty($old_rules)) {
            add_settings_error('woo_order_filter', 'no_migration', 'Aucune règle à migrer.', 'error');
            return false;
        }
        
        $migrated_rules = [];
        
        foreach ($old_rules as $role => $statuses) {
            $migrated_rules[$role] = [
                'view' => $statuses,   // Les anciens statuts deviennent visibles
                'change' => []         // Aucun statut modifiable par défaut
            ];
        }
        
        update_option('woo_order_filter_rules_v2', $migrated_rules);
        update_option('woo_order_filter_rules_backup', $old_rules); // Sauvegarde
        
        add_settings_error('woo_order_filter', 'migration_success', 
                          'Migration réussie ! ' . count($old_rules) . ' règle(s) migrée(s).', 'updated');
        
        return true;
    }
    
    /**
     * 🎯 Fonction utilitaire pour récupérer les permissions d'un utilisateur
     */
    public static function get_user_order_permissions($user_roles) {
        // Nouveau format d'abord
        $new_rules = get_option('woo_order_filter_rules_v2', []);
        
        if (!empty($new_rules)) {
            $view_statuses = [];
            $change_statuses = [];
            
            foreach ($user_roles as $role) {
                if (isset($new_rules[$role])) {
                    if (isset($new_rules[$role]['view'])) {
                        $view_statuses = array_merge($view_statuses, $new_rules[$role]['view']);
                    }
                    if (isset($new_rules[$role]['change'])) {
                        $change_statuses = array_merge($change_statuses, $new_rules[$role]['change']);
                    }
                }
            }
            
            return [
                'view' => array_unique($view_statuses),
                'change' => array_unique($change_statuses)
            ];
        }
        
        // Fallback ancien format
        $old_rules = get_option('woo_order_filter_rules', []);
        $view_statuses = [];
        
        foreach ($user_roles as $role) {
            if (isset($old_rules[$role])) {
                $view_statuses = array_merge($view_statuses, $old_rules[$role]);
            }
        }
        
        return [
            'view' => array_unique($view_statuses),
            'change' => []
        ];
    }
}

?>