<?php
require_once('../wp-load.php'); // Ajuste ce chemin selon l'emplacement de ce fichier

// 🔐 Rediriger si l'utilisateur n'est pas connecté
if (!is_user_logged_in()) {
    wp_redirect('login.php');
    exit;
}

// Vérifier que WooCommerce est disponible
if (!class_exists('WooCommerce')) {
    wp_die('WooCommerce doit être activé pour utiliser cette page.');
}

// Inclure la classe du plugin si elle n'est pas déjà chargée
if (!class_exists('WooOrderStatusRoleFilter')) {
    require_once('woo-orders.php');
}

$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_roles = $current_user->roles;

// 🎯 Utiliser la nouvelle méthode pour récupérer les permissions
$permissions = WooOrderStatusRoleFilter::get_user_order_permissions($user_roles);
$view_statuses = $permissions['view'];
$change_statuses = $permissions['change'];

// Vérifier les permissions
if (empty($view_statuses)) {
    echo "<h3>Vous n’avez pas accès aux commandes.</h3>";
    exit;
}

// � Traitement de la déconnexion
if (isset($_POST['logout_submit'])) {
    if (wp_verify_nonce($_POST['logout_nonce'], 'user_logout')) {
        wp_logout();
        wp_redirect('login.php');
        exit;
    }
}

// 🧾 Vérifier si l'utilisateur a des permissions d'administration (AVANT le traitement)
$is_admin_user = current_user_can('manage_options') || current_user_can('edit_shop_orders');

// 👤 Convertir les rôles en descriptions lisibles
function get_role_display_names($roles) {
    $role_names = [];
    $wp_roles = wp_roles();

    foreach ($roles as $role) {
        if (isset($wp_roles->roles[$role])) {
            $role_names[] = translate_user_role($wp_roles->roles[$role]['name']);
        } else {
            // Fallback pour les rôles personnalisés
            $role_names[] = ucfirst(str_replace('_', ' ', $role));
        }
    }

    return $role_names;
}

$user_role_names = get_role_display_names($user_roles);

// �🔄 Traitement de la modification de statut
$status_updated = false;
$update_message = '';

if (isset($_POST['update_order_status']) && isset($_POST['order_id']) && isset($_POST['new_status'])) {
    $order_id = intval($_POST['order_id']);
    $new_status = sanitize_text_field($_POST['new_status']);
    $status_comment = isset($_POST['status_comment']) ? sanitize_textarea_field($_POST['status_comment']) : '';
    $assign_user = isset($_POST['assign_user']) ? intval($_POST['assign_user']) : 0;

    // Vérifier le nonce pour la sécurité
    if (wp_verify_nonce($_POST['status_nonce'], 'update_order_status_' . $order_id)) {
        // Vérifier que l'utilisateur peut modifier ce statut
        if (in_array($new_status, $change_statuses)) {
            $order = wc_get_order($order_id);

            // Vérifier les permissions : administrateur ou propriétaire de la commande
            $can_modify = false;
            if ($is_admin_user) {
                $can_modify = true; // Les administrateurs peuvent modifier toutes les commandes
            } elseif ($order && $order->get_customer_id() == $user_id) {
                $can_modify = true; // Les clients peuvent modifier leurs propres commandes
            }

            if ($order && $can_modify) {
                $old_status = $order->get_status();
                $old_status_name = wc_get_order_status_name($old_status);
                $new_status_name = wc_get_order_status_name($new_status);

                // Construire le message de note
                $note_message = "Statut modifié par " . $current_user->display_name . " via l'interface frontend.";
                if (!empty($status_comment)) {
                    $note_message .= "\nCommentaire: " . $status_comment;
                }
                if ($assign_user && $assign_user != $current_user->ID) {
                    $assigned_user = get_user_by('ID', $assign_user);
                    if ($assigned_user) {
                        $note_message .= "\nAssigné à: " . $assigned_user->display_name;
                        // Ajouter une meta pour l'utilisateur assigné
                        $order->update_meta_data('_assigned_user_id', $assign_user);
                        $order->update_meta_data('_assigned_user_name', $assigned_user->display_name);
                    }
                }

                $order->update_status($new_status, $note_message);
                $order->save();

                $status_updated = true;
                $success_message = "Statut de la commande #{$order_id} modifié de '{$old_status_name}' vers '{$new_status_name}'.";
                if (!empty($status_comment)) {
                    $success_message .= " Commentaire ajouté.";
                }
                if ($assign_user && $assign_user != $current_user->ID) {
                    $assigned_user = get_user_by('ID', $assign_user);
                    if ($assigned_user) {
                        $success_message .= " Assigné à " . $assigned_user->display_name . ".";
                    }
                }
                $update_message = $success_message;
            } else {
                $update_message = "Erreur : Commande non trouvée ou non autorisée.";
            }
        } else {
            $update_message = "Erreur : Vous n'avez pas l'autorisation de modifier vers ce statut.";
        }
    } else {
        $update_message = "Erreur : Token de sécurité invalide.";
    }
}

// 🧾 Récupération des commandes avec statuts filtrés

if ($is_admin_user) {
    // Pour les administrateurs : afficher toutes les commandes avec les statuts autorisés
    $orders = wc_get_orders([
        'status'      => $view_statuses,
        'limit'       => 50,
        'orderby'     => 'date',
        'order'       => 'DESC'
    ]);
} else {
    // Pour les clients : afficher seulement leurs commandes
    $orders = wc_get_orders([
        'customer_id' => $user_id,
        'status'      => $view_statuses,
        'limit'       => 20,
        'orderby'     => 'date',
        'order'       => 'DESC'
    ]);
}

// Debug : afficher les informations de débogage
$debug_info = [
    'user_id' => $user_id,
    'user_roles' => $user_roles,
    'is_admin' => $is_admin_user,
    'view_statuses' => $view_statuses,
    'change_statuses' => $change_statuses,
    'total_orders_found' => count($orders)
];

// Récupérer tous les statuts disponibles pour les sélecteurs
$all_order_statuses = wc_get_order_statuses();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Mes commandes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #f58a2c;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .permissions-info {
            background: #fff8f0;
            border: 1px solid #f58a2c;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .permissions-info h3 {
            margin-top: 0;
            color: #f58a2c;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
            margin: 2px;
        }
        .status-pending { background-color: #ffba00; }
        .status-processing { background-color: #c6e1c6; color: #5b841b; }
        .status-on-hold { background-color: #f8dda7; color: #94660c; }
        .status-completed { background-color: #c8d7e1; color: #2e4453; }
        .status-cancelled { background-color: #eba3a3; color: #761919; }
        .status-refunded { background-color: #eba3a3; color: #761919; }
        .status-failed { background-color: #eba3a3; color: #761919; }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f8ff;
        }
        .status-form {
            display: inline-block;
        }
        .status-select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            margin-right: 5px;
        }
        .update-btn {
            background: #f58a2c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
        }
        .update-btn:hover {
            background: #e67e22;
        }
        .update-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            padding: 10px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .no-permissions {
            color: #666;
            font-style: italic;
        }
        .view-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
            margin-right: 5px;
        }
        .view-btn:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }
        .modify-btn {
            background: #f58a2c;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
            margin-right: 5px;
        }
        .modify-btn:hover {
            background: #e67e22;
            transform: translateY(-1px);
        }
        .cancel-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .cancel-btn:hover {
            background: #5a6268;
        }
        .actions-cell {
            white-space: nowrap;
            text-align: center;
        }
        .status-cell {
            white-space: nowrap;
            vertical-align: middle;
        }
        .status-cell .status-form {
            display: inline-block;
            margin-left: 10px;
        }
        .status-select {
            padding: 5px 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-size: 12px;
            background: white;
        }
        .order-details {
            display: none;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        .order-details.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }
        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                padding-top: 0;
                padding-bottom: 0;
            }
            to {
                opacity: 1;
                max-height: 500px;
                padding-top: 15px;
                padding-bottom: 15px;
            }
        }
        .order-details h4 {
            color: #f58a2c;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .order-items {
            margin-bottom: 15px;
        }
        .order-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            gap: 15px;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid #ddd;
            flex-shrink: 0;
        }
        .product-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .product-details {
            flex: 1;
        }
        .item-name {
            font-weight: 500;
            color: #333;
        }
        .item-quantity {
            color: #666;
            font-size: 14px;
        }
        .item-price {
            font-weight: bold;
            color: #f58a2c;
        }
        .order-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .meta-item {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .meta-label {
            font-weight: bold;
            color: #495057;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .meta-value {
            color: #333;
        }
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .logout-btn:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        /* Styles pour le panneau de modification de statut */
        .status-modification-panel {
            background: #f8f9fa;
            border: 2px solid #f58a2c;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .panel-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .panel-icon {
            font-size: 16px;
            margin-right: 8px;
        }
        .panel-title {
            color: #f58a2c;
            font-weight: bold;
            font-size: 16px;
        }
        .modification-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        .modification-column {
            display: flex;
            flex-direction: column;
        }
        .column-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        .column-icon {
            font-size: 14px;
            margin-right: 6px;
        }
        .column-title {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }
        .modification-column select,
        .modification-column textarea {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            min-height: 38px;
        }
        .comment-textarea {
            min-height: 60px;
            resize: vertical;
            font-family: inherit;
        }
        .modification-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: 10px;
        }
        .validate-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .validate-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }
        .status-details-row {
            background: #fff8f0;
        }

        /* Responsive pour mobile */
        @media (max-width: 768px) {
            .modification-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .modification-actions {
                justify-content: center;
            }
        }

    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1>🛒 Mes commandes WooCommerce</h1>
                <p>Utilisateur : <strong><?php echo esc_html($current_user->display_name); ?></strong>
                   (<?php echo esc_html(implode(', ', $user_role_names)); ?>)</p>
                <p style="color: #666; font-size: 14px; margin-top: 5px;">
                    📊 Total des commandes : <strong><?php echo count($orders); ?></strong>
                </p>
            </div>
            <form method="post" style="margin: 0;">
                <?php wp_nonce_field('user_logout', 'logout_nonce'); ?>
                <button type="submit" name="logout_submit" class="logout-btn"
                        onclick="return confirm('Êtes-vous sûr de vouloir vous déconnecter ?')">
                    🚪 Déconnexion
                </button>
            </form>
        </div>
    </div>

    <!-- Affichage des permissions -->
    <div class="permissions-info">
        <h3>📋 Vos permissions</h3>
        <p><strong>👁️ Statuts visibles :</strong>
            <?php if (empty($view_statuses)): ?>
                <span class="no-permissions">Aucun</span>
            <?php else: ?>
                <?php foreach ($view_statuses as $status): ?>
                    <span class="status-badge status-<?php echo esc_attr(str_replace('wc-', '', $status)); ?>">
                        <?php echo esc_html($all_order_statuses[$status] ?? $status); ?>
                    </span>
                <?php endforeach; ?>
            <?php endif; ?>
        </p>
        <p><strong>✏️ Statuts modifiables :</strong>
            <?php if (empty($change_statuses)): ?>
                <span class="no-permissions">Aucun</span>
            <?php else: ?>
                <?php foreach ($change_statuses as $status): ?>
                    <span class="status-badge status-<?php echo esc_attr(str_replace('wc-', '', $status)); ?>">
                        <?php echo esc_html($all_order_statuses[$status] ?? $status); ?>
                    </span>
                <?php endforeach; ?>
            <?php endif; ?>
        </p>
    </div>



    <!-- Message de mise à jour -->
    <?php if (!empty($update_message)): ?>
        <div class="message <?php echo $status_updated ? 'success' : 'error'; ?>">
            <?php echo esc_html($update_message); ?>
        </div>
    <?php endif; ?>

    <!-- Liste des commandes -->
    <?php if (empty($orders)) : ?>
        <p>Aucune commande à afficher avec vos permissions actuelles.</p>
    <?php else : ?>
        <table>
            <thead>
                <tr>
                    <th>ID Commande</th>
                    <th>Date</th>
                    <th>Total</th>
                    <th>Statut actuel</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($orders as $order): ?>
                    <tr>
                        <td><strong>#<?php echo $order->get_id(); ?></strong></td>
                        <td><?php echo $order->get_date_created()->date_i18n('d/m/Y H:i'); ?></td>
                        <td><strong><?php echo $order->get_formatted_order_total(); ?></strong></td>
                        <td class="status-cell">
                            <span class="status-badge status-<?php echo esc_attr(str_replace('wc-', '', $order->get_status())); ?>">
                                <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                            </span>
                        </td>
                        <td class="actions-cell">
                            <!-- Bouton Voir -->
                            <button type="button" class="view-btn" onclick="toggleOrderDetails(<?php echo $order->get_id(); ?>)">
                                👁️ Voir
                            </button>

                            <!-- Bouton Modifier (si permissions) -->
                            <?php if (!empty($change_statuses)): ?>
                                <button type="button" class="modify-btn" onclick="toggleStatusDetails(<?php echo $order->get_id(); ?>)">
                                    ✏️ Modifier
                                </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <!-- Panneau de modification de statut -->
                    <tr id="status-details-<?php echo $order->get_id(); ?>" class="status-details-row" style="display: none;">
                        <td colspan="5">
                            <div class="status-modification-panel">
                                <div class="panel-header">
                                    <span class="panel-icon">🔄</span>
                                    <span class="panel-title">Modifier le statut de la commande #<?php echo $order->get_id(); ?></span>
                                </div>

                                <form method="post" class="status-form" id="status-form-<?php echo $order->get_id(); ?>">
                                    <?php wp_nonce_field('update_order_status_' . $order->get_id(), 'status_nonce'); ?>
                                    <input type="hidden" name="order_id" value="<?php echo $order->get_id(); ?>">
                                    <input type="hidden" name="update_order_status" value="1">

                                    <div class="modification-grid">
                                        <!-- Colonne 1: Nouveau Statut -->
                                        <div class="modification-column">
                                            <div class="column-header">
                                                <span class="column-icon">📋</span>
                                                <span class="column-title">Nouveau Statut</span>
                                            </div>
                                            <select name="new_status" class="status-select" id="new-status-<?php echo $order->get_id(); ?>" required>
                                                <option value="">-- Choisir un statut --</option>
                                                <?php foreach ($change_statuses as $status_key): ?>
                                                    <?php if ($status_key !== $order->get_status()): ?>
                                                        <option value="<?php echo esc_attr($status_key); ?>">
                                                            <?php echo esc_html($all_order_statuses[$status_key] ?? $status_key); ?>
                                                        </option>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <!-- Colonne 2: Statuts visibles -->
                                        <div class="modification-column">
                                            <div class="column-header">
                                                <span class="column-icon">�️</span>
                                                <span class="column-title">Statuts visibles</span>
                                            </div>
                                            <select name="visible_status" class="status-select" id="visible-status-<?php echo $order->get_id(); ?>">
                                                <option value="">-- Choisir un statut --</option>
                                                <?php foreach ($view_statuses as $status_key): ?>
                                                    <option value="<?php echo esc_attr($status_key); ?>" <?php selected($status_key, $order->get_status()); ?>>
                                                        <?php echo esc_html($all_order_statuses[$status_key] ?? $status_key); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <!-- Colonne 3: Commentaire -->
                                        <div class="modification-column">
                                            <div class="column-header">
                                                <span class="column-icon">💬</span>
                                                <span class="column-title">Commentaire</span>
                                            </div>
                                            <textarea name="status_comment" id="status-comment-<?php echo $order->get_id(); ?>"
                                                      class="comment-textarea"
                                                      placeholder="Commentaire optionnel sur le changement de statut..."></textarea>
                                        </div>
                                    </div>

                                    <div class="modification-actions">
                                        <button type="submit" class="validate-btn">
                                            ✅ Valider les modifications
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </td>
                    </tr>

                    <tr id="details-<?php echo $order->get_id(); ?>" class="order-details-row" style="display: none;">
                        <td colspan="5">
                            <div class="order-details" id="order-details-<?php echo $order->get_id(); ?>">
                                <h4>📦 Détails de la commande #<?php echo $order->get_id(); ?></h4>

                                <!-- Articles de la commande -->
                                <div class="order-items">
                                    <h5>🛍️ Articles commandés :</h5>
                                    <?php foreach ($order->get_items() as $item_id => $item): ?>
                                        <?php
                                        $product = $item->get_product();
                                        $product_name = $item->get_name();
                                        $quantity = $item->get_quantity();
                                        $total = $item->get_total();

                                        // Récupérer l'image du produit
                                        $product_image = '';
                                        if ($product) {
                                            $image_id = $product->get_image_id();
                                            if ($image_id) {
                                                $product_image = wp_get_attachment_image_url($image_id, 'thumbnail');
                                            }
                                        }

                                        // Image par défaut si aucune image trouvée
                                        if (!$product_image) {
                                            $product_image = wc_placeholder_img_src('thumbnail');
                                        }
                                        ?>
                                        <div class="order-item">
                                            <!-- Image du produit -->
                                            <img src="<?php echo esc_url($product_image); ?>"
                                                 alt="<?php echo esc_attr($product_name); ?>"
                                                 class="product-image"
                                                 onerror="this.src='<?php echo esc_url(wc_placeholder_img_src('thumbnail')); ?>'">

                                            <!-- Informations du produit -->
                                            <div class="product-info">
                                                <div class="product-details">
                                                    <div class="item-name"><?php echo esc_html($product_name); ?></div>
                                                    <div class="item-quantity" style="color: #666; font-size: 14px; margin-top: 2px;">
                                                        Quantité: <?php echo $quantity; ?>
                                                        <?php if ($product && $product->get_sku()): ?>
                                                            • SKU: <?php echo esc_html($product->get_sku()); ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="item-price"><?php echo wc_price($total); ?></div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Informations de la commande -->
                                <div class="order-meta">
                                    <div class="meta-item">
                                        <div class="meta-label">📅 Date de commande</div>
                                        <div class="meta-value"><?php echo $order->get_date_created()->date_i18n('d/m/Y à H:i'); ?></div>
                                    </div>

                                    <div class="meta-item">
                                        <div class="meta-label">💳 Mode de paiement</div>
                                        <div class="meta-value"><?php echo esc_html($order->get_payment_method_title() ?: 'Non spécifié'); ?></div>
                                    </div>

                                    <div class="meta-item">
                                        <div class="meta-label">💰 Total de la commande</div>
                                        <div class="meta-value" style="font-weight: bold; color: #f58a2c; font-size: 16px;">
                                            <?php echo $order->get_formatted_order_total(); ?>
                                        </div>
                                    </div>

                                    <div class="meta-item">
                                        <div class="meta-label">� Téléphone</div>
                                        <div class="meta-value"><?php echo esc_html($order->get_billing_phone() ?: 'Non spécifié'); ?></div>
                                    </div>

                                    <?php if ($order->get_total_shipping() > 0): ?>
                                    <div class="meta-item">
                                        <div class="meta-label">🚚 Frais de livraison</div>
                                        <div class="meta-value"><?php echo wc_price($order->get_total_shipping()); ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($order->get_total_tax() > 0): ?>
                                    <div class="meta-item">
                                        <div class="meta-label">🧾 Taxes</div>
                                        <div class="meta-value"><?php echo wc_price($order->get_total_tax()); ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="meta-item">
                                        <div class="meta-label">📍 Adresse de livraison</div>
                                        <div class="meta-value">
                                            <?php
                                            $shipping_address = $order->get_formatted_shipping_address();
                                            echo $shipping_address ? $shipping_address : 'Non spécifiée';
                                            ?>
                                        </div>
                                    </div>
                                </div>

                                <?php if ($order->get_customer_note()): ?>
                                <div class="meta-item" style="grid-column: 1 / -1; margin-top: 10px;">
                                    <div class="meta-label">💬 Note du client</div>
                                    <div class="meta-value"><?php echo esc_html($order->get_customer_note()); ?></div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>

    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px;">
        <p>💡 <strong>Aide :</strong> Les statuts que vous pouvez voir et modifier dépendent de votre rôle utilisateur.
           Contactez l'administrateur pour modifier vos permissions.</p>
    </div>
</div>

<script>
// Validation simple avant modification de statut (sans confirmation)
document.querySelectorAll('.status-form').forEach(form => {
    form.addEventListener('submit', function(e) {
        const select = this.querySelector('select[name="new_status"]');
        if (!select.value) {
            e.preventDefault();
            alert('Veuillez sélectionner un nouveau statut.');
            return;
        }
    });
});

// Animation au survol des boutons de visualisation
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-1px) scale(1)';
        });
    });
});

// Fermer tous les détails ouverts quand on en ouvre un nouveau
let currentOpenDetails = null;
function toggleOrderDetails(orderId) {
    const detailsRow = document.getElementById('details-' + orderId);
    const detailsDiv = document.getElementById('order-details-' + orderId);
    const button = event.target;

    // Fermer le détail précédemment ouvert
    if (currentOpenDetails && currentOpenDetails !== orderId) {
        const prevDetailsRow = document.getElementById('details-' + currentOpenDetails);
        const prevDetailsDiv = document.getElementById('order-details-' + currentOpenDetails);
        const prevButton = document.querySelector(`[onclick="toggleOrderDetails(${currentOpenDetails})"]`);

        if (prevDetailsRow && prevDetailsDiv && prevButton) {
            prevDetailsDiv.classList.remove('show');
            setTimeout(() => {
                prevDetailsRow.style.display = 'none';
            }, 300);
            prevButton.innerHTML = '👁️ Voir';
            prevButton.style.background = '#6c757d';
        }
    }

    if (detailsRow.style.display === 'none' || detailsRow.style.display === '') {
        // Afficher les détails
        detailsRow.style.display = 'table-row';
        detailsDiv.classList.add('show');
        button.innerHTML = '👁️ Masquer';
        button.style.background = '#dc3545';
        currentOpenDetails = orderId;

        // Scroll vers les détails
        setTimeout(() => {
            detailsDiv.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 100);
    } else {
        // Masquer les détails
        detailsDiv.classList.remove('show');
        setTimeout(() => {
            detailsRow.style.display = 'none';
        }, 300);
        button.innerHTML = '👁️ Voir';
        button.style.background = '#6c757d';
        currentOpenDetails = null;
    }
}

// Fonction pour afficher/masquer le panneau de modification de statut
function toggleStatusDetails(orderId) {
    const statusDetailsRow = document.getElementById('status-details-' + orderId);
    const button = event.target;

    // Fermer tous les autres panneaux ouverts
    document.querySelectorAll('.status-details-row').forEach(row => {
        if (row.id !== 'status-details-' + orderId && row.style.display !== 'none') {
            row.style.display = 'none';
            // Remettre le bouton correspondant à l'état normal
            const otherOrderId = row.id.replace('status-details-', '');
            const otherButton = document.querySelector(`[onclick="toggleStatusDetails(${otherOrderId})"]`);
            if (otherButton) {
                otherButton.innerHTML = '✏️ Modifier';
                otherButton.style.background = '#f58a2c';
            }
        }
    });

    if (statusDetailsRow.style.display === 'none' || statusDetailsRow.style.display === '') {
        // Afficher le panneau
        statusDetailsRow.style.display = 'table-row';
        button.innerHTML = '✏️ Fermer';
        button.style.background = '#6c757d';

        // Scroll vers le panneau
        setTimeout(() => {
            statusDetailsRow.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 100);
    } else {
        // Masquer le panneau
        statusDetailsRow.style.display = 'none';
        button.innerHTML = '✏️ Modifier';
        button.style.background = '#f58a2c';
    }
}
</script>

</body>
</html>