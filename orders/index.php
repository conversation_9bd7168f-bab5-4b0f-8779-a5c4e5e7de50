<?php
require_once('../wp-load.php'); // Ajuste ce chemin selon l'emplacement de ce fichier

// 🔐 Rediriger si l'utilisateur n'est pas connecté
if (!is_user_logged_in()) {
    wp_redirect('login.php');
    exit;
}

// Vérifier que WooCommerce est disponible
if (!class_exists('WooCommerce')) {
    wp_die('WooCommerce doit être activé pour utiliser cette page.');
}

// Inclure la classe du plugin si elle n'est pas déjà chargée
if (!class_exists('WooOrderStatusRoleFilter')) {
    require_once('woo-orders.php');
}

$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_roles = $current_user->roles;

// 🎯 Utiliser la nouvelle méthode pour récupérer les permissions
$permissions = WooOrderStatusRoleFilter::get_user_order_permissions($user_roles);
$view_statuses = $permissions['view'];
$change_statuses = $permissions['change'];

// Vérifier les permissions
if (empty($view_statuses)) {
    echo "<h3>Vous n’avez pas accès aux commandes.</h3>";
    exit;
}

// � Traitement de la déconnexion
if (isset($_POST['logout_submit'])) {
    if (wp_verify_nonce($_POST['logout_nonce'], 'user_logout')) {
        wp_logout();
        wp_redirect('login.php');
        exit;
    }
}

// �🔄 Traitement de la modification de statut
$status_updated = false;
$update_message = '';

if (isset($_POST['update_order_status']) && isset($_POST['order_id']) && isset($_POST['new_status'])) {
    $order_id = intval($_POST['order_id']);
    $new_status = sanitize_text_field($_POST['new_status']);

    // Vérifier le nonce pour la sécurité
    if (wp_verify_nonce($_POST['status_nonce'], 'update_order_status_' . $order_id)) {
        // Vérifier que l'utilisateur peut modifier ce statut
        if (in_array($new_status, $change_statuses)) {
            $order = wc_get_order($order_id);
            if ($order && $order->get_customer_id() == $user_id) {
                $old_status = $order->get_status();
                $order->update_status($new_status, 'Statut modifié par l\'utilisateur via l\'interface frontend.');
                $status_updated = true;
                $update_message = "Statut de la commande #{$order_id} modifié de '{$old_status}' vers '{$new_status}'.";
            } else {
                $update_message = "Erreur : Commande non trouvée ou non autorisée.";
            }
        } else {
            $update_message = "Erreur : Vous n'avez pas l'autorisation de modifier vers ce statut.";
        }
    } else {
        $update_message = "Erreur : Token de sécurité invalide.";
    }
}

// 🧾 Récupération des commandes du user connecté avec statuts filtrés
$orders = wc_get_orders([
    'customer_id' => $user_id,
    'status'      => $view_statuses,
    'limit'       => 20,
    'orderby'     => 'date',
    'order'       => 'DESC'
]);

// Récupérer tous les statuts disponibles pour les sélecteurs
$all_order_statuses = wc_get_order_statuses();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Mes commandes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #0073aa;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .permissions-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .permissions-info h3 {
            margin-top: 0;
            color: #0073aa;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
            margin: 2px;
        }
        .status-pending { background-color: #ffba00; }
        .status-processing { background-color: #c6e1c6; color: #5b841b; }
        .status-on-hold { background-color: #f8dda7; color: #94660c; }
        .status-completed { background-color: #c8d7e1; color: #2e4453; }
        .status-cancelled { background-color: #eba3a3; color: #761919; }
        .status-refunded { background-color: #eba3a3; color: #761919; }
        .status-failed { background-color: #eba3a3; color: #761919; }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f8ff;
        }
        .status-form {
            display: inline-block;
        }
        .status-select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            margin-right: 5px;
        }
        .update-btn {
            background: #0073aa;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .update-btn:hover {
            background: #005a87;
        }
        .update-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            padding: 10px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .no-permissions {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h1>🛒 Mes commandes WooCommerce</h1>
        <p>Utilisateur : <strong><?php echo esc_html($current_user->display_name); ?></strong>
           (<?php echo esc_html(implode(', ', $user_roles)); ?>)</p>
    </div>

    <!-- Affichage des permissions -->
    <div class="permissions-info">
        <h3>📋 Vos permissions</h3>
        <p><strong>👁️ Statuts visibles :</strong>
            <?php if (empty($view_statuses)): ?>
                <span class="no-permissions">Aucun</span>
            <?php else: ?>
                <?php foreach ($view_statuses as $status): ?>
                    <span class="status-badge status-<?php echo esc_attr(str_replace('wc-', '', $status)); ?>">
                        <?php echo esc_html($all_order_statuses[$status] ?? $status); ?>
                    </span>
                <?php endforeach; ?>
            <?php endif; ?>
        </p>
        <p><strong>✏️ Statuts modifiables :</strong>
            <?php if (empty($change_statuses)): ?>
                <span class="no-permissions">Aucun</span>
            <?php else: ?>
                <?php foreach ($change_statuses as $status): ?>
                    <span class="status-badge status-<?php echo esc_attr(str_replace('wc-', '', $status)); ?>">
                        <?php echo esc_html($all_order_statuses[$status] ?? $status); ?>
                    </span>
                <?php endforeach; ?>
            <?php endif; ?>
        </p>
    </div>

    <!-- Message de mise à jour -->
    <?php if (!empty($update_message)): ?>
        <div class="message <?php echo $status_updated ? 'success' : 'error'; ?>">
            <?php echo esc_html($update_message); ?>
        </div>
    <?php endif; ?>

    <!-- Liste des commandes -->
    <?php if (empty($orders)) : ?>
        <p>Aucune commande à afficher avec vos permissions actuelles.</p>
    <?php else : ?>
        <table>
            <thead>
                <tr>
                    <th>ID Commande</th>
                    <th>Date</th>
                    <th>Statut actuel</th>
                    <th>Total</th>
                    <?php if (!empty($change_statuses)): ?>
                        <th>Modifier statut</th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($orders as $order): ?>
                    <tr>
                        <td><strong>#<?php echo $order->get_id(); ?></strong></td>
                        <td><?php echo $order->get_date_created()->date_i18n('d/m/Y H:i'); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo esc_attr(str_replace('wc-', '', $order->get_status())); ?>">
                                <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                            </span>
                        </td>
                        <td><strong><?php echo $order->get_formatted_order_total(); ?></strong></td>
                        <?php if (!empty($change_statuses)): ?>
                            <td>
                                <form method="post" class="status-form">
                                    <?php wp_nonce_field('update_order_status_' . $order->get_id(), 'status_nonce'); ?>
                                    <input type="hidden" name="order_id" value="<?php echo $order->get_id(); ?>">
                                    <select name="new_status" class="status-select">
                                        <option value="">-- Choisir --</option>
                                        <?php foreach ($change_statuses as $status_key): ?>
                                            <?php if ($status_key !== $order->get_status()): ?>
                                                <option value="<?php echo esc_attr($status_key); ?>">
                                                    <?php echo esc_html($all_order_statuses[$status_key] ?? $status_key); ?>
                                                </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </select>
                                    <button type="submit" name="update_order_status" class="update-btn">
                                        Modifier
                                    </button>
                                </form>
                            </td>
                        <?php endif; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>

    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px;">
        <p>💡 <strong>Aide :</strong> Les statuts que vous pouvez voir et modifier dépendent de votre rôle utilisateur.
           Contactez l'administrateur pour modifier vos permissions.</p>
    </div>
</div>

<script>
// Confirmation avant modification de statut
document.querySelectorAll('.status-form').forEach(form => {
    form.addEventListener('submit', function(e) {
        const select = this.querySelector('select[name="new_status"]');
        if (!select.value) {
            e.preventDefault();
            alert('Veuillez sélectionner un nouveau statut.');
            return;
        }

        const orderId = this.querySelector('input[name="order_id"]').value;
        const newStatus = select.options[select.selectedIndex].text;

        if (!confirm(`Êtes-vous sûr de vouloir modifier le statut de la commande #${orderId} vers "${newStatus}" ?`)) {
            e.preventDefault();
        }
    });
});
</script>

</body>
</html>