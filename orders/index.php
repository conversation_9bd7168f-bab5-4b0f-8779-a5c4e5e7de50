<?php
require_once('../wp-load.php'); // Ajuste ce chemin selon l'emplacement de ce fichier

// 🔐 Rediriger si l'utilisateur n'est pas connecté
if (!is_user_logged_in()) {
    wp_redirect('login.php');
    exit;
}

// Vérifier que WooCommerce est disponible
if (!class_exists('WooCommerce')) {
    wp_die('WooCommerce doit être activé pour utiliser cette page.');
}

// Inclure la classe du plugin si elle n'est pas déjà chargée
if (!class_exists('WooOrderStatusRoleFilter')) {
    require_once('woo-orders.php');
}

$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_roles = $current_user->roles;

// 🎯 Utiliser la nouvelle méthode pour récupérer les permissions
$permissions = WooOrderStatusRoleFilter::get_user_order_permissions($user_roles);
$view_statuses = $permissions['view'];
$change_statuses = $permissions['change'];

// Vérifier les permissions
if (empty($view_statuses)) {
    echo "<h3>Vous n’avez pas accès aux commandes.</h3>";
    exit;
}

// � Traitement de la déconnexion
if (isset($_POST['logout_submit'])) {
    if (wp_verify_nonce($_POST['logout_nonce'], 'user_logout')) {
        wp_logout();
        wp_redirect('login.php');
        exit;
    }
}

// �🔄 Traitement de la modification de statut
$status_updated = false;
$update_message = '';

if (isset($_POST['update_order_status']) && isset($_POST['order_id']) && isset($_POST['new_status'])) {
    $order_id = intval($_POST['order_id']);
    $new_status = sanitize_text_field($_POST['new_status']);

    // Vérifier le nonce pour la sécurité
    if (wp_verify_nonce($_POST['status_nonce'], 'update_order_status_' . $order_id)) {
        // Vérifier que l'utilisateur peut modifier ce statut
        if (in_array($new_status, $change_statuses)) {
            $order = wc_get_order($order_id);
            if ($order && $order->get_customer_id() == $user_id) {
                $old_status = $order->get_status();
                $order->update_status($new_status, 'Statut modifié par l\'utilisateur via l\'interface frontend.');
                $status_updated = true;
                $update_message = "Statut de la commande #{$order_id} modifié de '{$old_status}' vers '{$new_status}'.";
            } else {
                $update_message = "Erreur : Commande non trouvée ou non autorisée.";
            }
        } else {
            $update_message = "Erreur : Vous n'avez pas l'autorisation de modifier vers ce statut.";
        }
    } else {
        $update_message = "Erreur : Token de sécurité invalide.";
    }
}

// 🧾 Récupération des commandes du user connecté avec statuts filtrés
$orders = wc_get_orders([
    'customer_id' => $user_id,
    'status'      => $view_statuses,
    'limit'       => 20,
    'orderby'     => 'date',
    'order'       => 'DESC'
]);

// Récupérer tous les statuts disponibles pour les sélecteurs
$all_order_statuses = wc_get_order_statuses();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Mes commandes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #0073aa;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .permissions-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .permissions-info h3 {
            margin-top: 0;
            color: #0073aa;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
            margin: 2px;
        }
        .status-pending { background-color: #ffba00; }
        .status-processing { background-color: #c6e1c6; color: #5b841b; }
        .status-on-hold { background-color: #f8dda7; color: #94660c; }
        .status-completed { background-color: #c8d7e1; color: #2e4453; }
        .status-cancelled { background-color: #eba3a3; color: #761919; }
        .status-refunded { background-color: #eba3a3; color: #761919; }
        .status-failed { background-color: #eba3a3; color: #761919; }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f8ff;
        }
        .status-form {
            display: inline-block;
        }
        .status-select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            margin-right: 5px;
        }
        .update-btn {
            background: #0073aa;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .update-btn:hover {
            background: #005a87;
        }
        .update-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            padding: 10px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .no-permissions {
            color: #666;
            font-style: italic;
        }
        .view-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }
        .view-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }
        .order-details {
            display: none;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        .order-details.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }
        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                padding-top: 0;
                padding-bottom: 0;
            }
            to {
                opacity: 1;
                max-height: 500px;
                padding-top: 15px;
                padding-bottom: 15px;
            }
        }
        .order-details h4 {
            color: #0073aa;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .order-items {
            margin-bottom: 15px;
        }
        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .item-name {
            font-weight: 500;
            color: #333;
        }
        .item-quantity {
            color: #666;
            font-size: 14px;
        }
        .item-price {
            font-weight: bold;
            color: #0073aa;
        }
        .order-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .meta-item {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .meta-label {
            font-weight: bold;
            color: #495057;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .meta-value {
            color: #333;
        }
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .logout-btn:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 14px;
        }
        .history-table th,
        .history-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .history-table th {
            background-color: #f1f3f4;
            font-weight: bold;
            color: #333;
            font-size: 12px;
        }
        .history-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .history-section {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }
        .history-section h5 {
            color: #0073aa;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .status-change {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .status-arrow {
            color: #666;
            font-weight: bold;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1>🛒 Mes commandes WooCommerce</h1>
                <p>Utilisateur : <strong><?php echo esc_html($current_user->display_name); ?></strong>
                   (<?php echo esc_html(implode(', ', $user_roles)); ?>)</p>
            </div>
            <form method="post" style="margin: 0;">
                <?php wp_nonce_field('user_logout', 'logout_nonce'); ?>
                <button type="submit" name="logout_submit" class="logout-btn"
                        onclick="return confirm('Êtes-vous sûr de vouloir vous déconnecter ?')">
                    🚪 Déconnexion
                </button>
            </form>
        </div>
    </div>

    <!-- Affichage des permissions -->
    <div class="permissions-info">
        <h3>📋 Vos permissions</h3>
        <p><strong>👁️ Statuts visibles :</strong>
            <?php if (empty($view_statuses)): ?>
                <span class="no-permissions">Aucun</span>
            <?php else: ?>
                <?php foreach ($view_statuses as $status): ?>
                    <span class="status-badge status-<?php echo esc_attr(str_replace('wc-', '', $status)); ?>">
                        <?php echo esc_html($all_order_statuses[$status] ?? $status); ?>
                    </span>
                <?php endforeach; ?>
            <?php endif; ?>
        </p>
        <p><strong>✏️ Statuts modifiables :</strong>
            <?php if (empty($change_statuses)): ?>
                <span class="no-permissions">Aucun</span>
            <?php else: ?>
                <?php foreach ($change_statuses as $status): ?>
                    <span class="status-badge status-<?php echo esc_attr(str_replace('wc-', '', $status)); ?>">
                        <?php echo esc_html($all_order_statuses[$status] ?? $status); ?>
                    </span>
                <?php endforeach; ?>
            <?php endif; ?>
        </p>
    </div>

    <!-- Message de mise à jour -->
    <?php if (!empty($update_message)): ?>
        <div class="message <?php echo $status_updated ? 'success' : 'error'; ?>">
            <?php echo esc_html($update_message); ?>
        </div>
    <?php endif; ?>

    <!-- Liste des commandes -->
    <?php if (empty($orders)) : ?>
        <p>Aucune commande à afficher avec vos permissions actuelles.</p>
    <?php else : ?>
        <table>
            <thead>
                <tr>
                    <th>ID Commande</th>
                    <th>Date</th>
                    <th>Statut actuel</th>
                    <th>Total</th>
                    <?php if (!empty($change_statuses)): ?>
                        <th>Modifier statut</th>
                    <?php endif; ?>
                    <th>Détails</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($orders as $order): ?>
                    <tr>
                        <td><strong>#<?php echo $order->get_id(); ?></strong></td>
                        <td><?php echo $order->get_date_created()->date_i18n('d/m/Y H:i'); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo esc_attr(str_replace('wc-', '', $order->get_status())); ?>">
                                <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                            </span>
                        </td>
                        <td><strong><?php echo $order->get_formatted_order_total(); ?></strong></td>
                        <?php if (!empty($change_statuses)): ?>
                            <td>
                                <form method="post" class="status-form">
                                    <?php wp_nonce_field('update_order_status_' . $order->get_id(), 'status_nonce'); ?>
                                    <input type="hidden" name="order_id" value="<?php echo $order->get_id(); ?>">
                                    <select name="new_status" class="status-select">
                                        <option value="">-- Choisir --</option>
                                        <?php foreach ($change_statuses as $status_key): ?>
                                            <?php if ($status_key !== $order->get_status()): ?>
                                                <option value="<?php echo esc_attr($status_key); ?>">
                                                    <?php echo esc_html($all_order_statuses[$status_key] ?? $status_key); ?>
                                                </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </select>
                                    <button type="submit" name="update_order_status" class="update-btn">
                                        Modifier
                                    </button>
                                </form>
                            </td>
                        <?php endif; ?>
                        <td>
                            <button type="button" class="view-btn" onclick="toggleOrderDetails(<?php echo $order->get_id(); ?>)">
                                👁️ Voir
                            </button>
                        </td>
                    </tr>
                    <tr id="details-<?php echo $order->get_id(); ?>" class="order-details-row" style="display: none;">
                        <td colspan="<?php echo !empty($change_statuses) ? '6' : '5'; ?>">
                            <div class="order-details" id="order-details-<?php echo $order->get_id(); ?>">
                                <h4>📦 Détails de la commande #<?php echo $order->get_id(); ?></h4>

                                <!-- Articles de la commande -->
                                <div class="order-items">
                                    <h5>🛍️ Articles commandés :</h5>
                                    <?php foreach ($order->get_items() as $item_id => $item): ?>
                                        <?php
                                        $product = $item->get_product();
                                        $product_name = $item->get_name();
                                        $quantity = $item->get_quantity();
                                        $total = $item->get_total();
                                        ?>
                                        <div class="order-item">
                                            <div>
                                                <span class="item-name"><?php echo esc_html($product_name); ?></span>
                                                <span class="item-quantity">(Qté: <?php echo $quantity; ?>)</span>
                                            </div>
                                            <span class="item-price"><?php echo wc_price($total); ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Informations de la commande -->
                                <div class="order-meta">
                                    <div class="meta-item">
                                        <div class="meta-label">📅 Date de commande</div>
                                        <div class="meta-value"><?php echo $order->get_date_created()->date_i18n('d/m/Y à H:i'); ?></div>
                                    </div>

                                    <div class="meta-item">
                                        <div class="meta-label">� Téléphone</div>
                                        <div class="meta-value"><?php echo esc_html($order->get_billing_phone() ?: 'Non spécifié'); ?></div>
                                    </div>

                                    <?php if ($order->get_total_shipping() > 0): ?>
                                    <div class="meta-item">
                                        <div class="meta-label">🚚 Frais de livraison</div>
                                        <div class="meta-value"><?php echo wc_price($order->get_total_shipping()); ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($order->get_total_tax() > 0): ?>
                                    <div class="meta-item">
                                        <div class="meta-label">🧾 Taxes</div>
                                        <div class="meta-value"><?php echo wc_price($order->get_total_tax()); ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="meta-item">
                                        <div class="meta-label">💳 Mode de paiement</div>
                                        <div class="meta-value"><?php echo esc_html($order->get_payment_method_title() ?: 'Non spécifié'); ?></div>
                                    </div>

                                    <div class="meta-item">
                                        <div class="meta-label">📍 Adresse de livraison</div>
                                        <div class="meta-value">
                                            <?php
                                            $shipping_address = $order->get_formatted_shipping_address();
                                            echo $shipping_address ? $shipping_address : 'Non spécifiée';
                                            ?>
                                        </div>
                                    </div>
                                </div>

                                <?php if ($order->get_customer_note()): ?>
                                <div class="meta-item" style="grid-column: 1 / -1; margin-top: 10px;">
                                    <div class="meta-label">💬 Note du client</div>
                                    <div class="meta-value"><?php echo esc_html($order->get_customer_note()); ?></div>
                                </div>
                                <?php endif; ?>

                                <!-- Historique des changements de statut -->
                                <div class="history-section">
                                    <h5>📋 Historique des statuts</h5>
                                    <?php
                                    // Récupérer les notes de commande (qui incluent les changements de statut)
                                    $order_notes = wc_get_order_notes(array(
                                        'order_id' => $order->get_id(),
                                        'order_by' => 'date_created',
                                        'order'    => 'DESC'
                                    ));

                                    $status_changes = array();

                                    // Filtrer pour ne garder que les changements de statut - critères plus larges
                                    foreach ($order_notes as $note) {
                                        $content_lower = strtolower($note->content);
                                        if (strpos($content_lower, 'statut') !== false ||
                                            strpos($content_lower, 'status') !== false ||
                                            strpos($note->content, 'Statut modifié') !== false ||
                                            $note->type === 'system') {
                                            $status_changes[] = $note;
                                        }
                                    }

                                    // Toujours ajouter le statut actuel comme première entrée
                                    $current_status_entry = (object) array(
                                        'date_created' => $order->get_date_modified() ?: $order->get_date_created(),
                                        'content' => 'Statut actuel : ' . wc_get_order_status_name($order->get_status()),
                                        'added_by' => 'Système',
                                        'type' => 'current'
                                    );
                                    array_unshift($status_changes, $current_status_entry);

                                    // Si aucun changement trouvé, créer un historique basique
                                    if (count($status_changes) <= 1) {
                                        $status_changes = array();

                                        // Statut actuel
                                        $status_changes[] = (object) array(
                                            'date_created' => $order->get_date_modified() ?: $order->get_date_created(),
                                            'content' => 'Statut actuel',
                                            'status' => $order->get_status(),
                                            'status_name' => wc_get_order_status_name($order->get_status()),
                                            'added_by' => 'Système',
                                            'type' => 'current'
                                        );

                                        // Statut initial (création)
                                        $status_changes[] = (object) array(
                                            'date_created' => $order->get_date_created(),
                                            'content' => 'Commande créée',
                                            'status' => 'pending',
                                            'status_name' => 'En attente de paiement',
                                            'added_by' => 'Client',
                                            'type' => 'creation'
                                        );
                                    }
                                    ?>

                                    <table class="history-table">
                                        <thead>
                                            <tr>
                                                <th>📅 Date</th>
                                                <th>🔄 Statut</th>
                                                <th>👤 Modifié par</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($status_changes as $index => $change): ?>
                                                <tr>
                                                    <td>
                                                        <?php
                                                        $date = $change->date_created;
                                                        if (is_object($date)) {
                                                            echo $date->date_i18n('d/m/Y H:i');
                                                        } else {
                                                            echo date_i18n('d/m/Y H:i', strtotime($date));
                                                        }
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        // Affichage simplifié et plus robuste
                                                        if (isset($change->type) && $change->type === 'current') {
                                                            echo '<span class="status-badge status-' . esc_attr(str_replace('wc-', '', $change->status)) . '">';
                                                            echo esc_html($change->status_name);
                                                            echo '</span> <em>(Actuel)</em>';
                                                        } elseif (isset($change->type) && $change->type === 'creation') {
                                                            echo '<span class="status-badge status-pending">';
                                                            echo esc_html($change->status_name);
                                                            echo '</span> <em>(Création)</em>';
                                                        } else {
                                                            $content = $change->content;

                                                            // Essayer d'extraire les statuts
                                                            if (preg_match('/de\s+["\']?([^"\']+)["\']?\s+vers?\s+["\']?([^"\'\.]+)["\']?/', $content, $matches)) {
                                                                $old_status = trim($matches[1]);
                                                                $new_status = trim($matches[2]);
                                                                echo '<div class="status-change">';
                                                                echo '<span class="status-badge">' . esc_html($old_status) . '</span>';
                                                                echo '<span class="status-arrow"> → </span>';
                                                                echo '<span class="status-badge">' . esc_html($new_status) . '</span>';
                                                                echo '</div>';
                                                            } else {
                                                                // Afficher le contenu tel quel
                                                                echo esc_html($content);
                                                            }
                                                        }
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        if (isset($change->added_by) && !empty($change->added_by)) {
                                                            echo esc_html($change->added_by);
                                                        } else {
                                                            echo 'Système';
                                                        }
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>

    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px;">
        <p>💡 <strong>Aide :</strong> Les statuts que vous pouvez voir et modifier dépendent de votre rôle utilisateur.
           Contactez l'administrateur pour modifier vos permissions.</p>
    </div>
</div>

<script>
// Confirmation avant modification de statut
document.querySelectorAll('.status-form').forEach(form => {
    form.addEventListener('submit', function(e) {
        const select = this.querySelector('select[name="new_status"]');
        if (!select.value) {
            e.preventDefault();
            alert('Veuillez sélectionner un nouveau statut.');
            return;
        }

        const orderId = this.querySelector('input[name="order_id"]').value;
        const newStatus = select.options[select.selectedIndex].text;

        if (!confirm(`Êtes-vous sûr de vouloir modifier le statut de la commande #${orderId} vers "${newStatus}" ?`)) {
            e.preventDefault();
        }
    });
});

// Animation au survol des boutons de visualisation
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-1px) scale(1)';
        });
    });
});

// Fermer tous les détails ouverts quand on en ouvre un nouveau
let currentOpenDetails = null;
function toggleOrderDetails(orderId) {
    const detailsRow = document.getElementById('details-' + orderId);
    const detailsDiv = document.getElementById('order-details-' + orderId);
    const button = event.target;

    // Fermer le détail précédemment ouvert
    if (currentOpenDetails && currentOpenDetails !== orderId) {
        const prevDetailsRow = document.getElementById('details-' + currentOpenDetails);
        const prevDetailsDiv = document.getElementById('order-details-' + currentOpenDetails);
        const prevButton = document.querySelector(`[onclick="toggleOrderDetails(${currentOpenDetails})"]`);

        if (prevDetailsRow && prevDetailsDiv && prevButton) {
            prevDetailsDiv.classList.remove('show');
            setTimeout(() => {
                prevDetailsRow.style.display = 'none';
            }, 300);
            prevButton.innerHTML = '👁️ Voir';
            prevButton.style.background = '#28a745';
        }
    }

    if (detailsRow.style.display === 'none' || detailsRow.style.display === '') {
        // Afficher les détails
        detailsRow.style.display = 'table-row';
        detailsDiv.classList.add('show');
        button.innerHTML = '👁️ Masquer';
        button.style.background = '#dc3545';
        currentOpenDetails = orderId;

        // Scroll vers les détails
        setTimeout(() => {
            detailsDiv.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 100);
    } else {
        // Masquer les détails
        detailsDiv.classList.remove('show');
        setTimeout(() => {
            detailsRow.style.display = 'none';
        }, 300);
        button.innerHTML = '👁️ Voir';
        button.style.background = '#28a745';
        currentOpenDetails = null;
    }
}
</script>

</body>
</html>