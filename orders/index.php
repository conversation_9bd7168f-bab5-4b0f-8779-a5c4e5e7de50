<?php
require_once('../wp-load.php'); // Ajuste ce chemin selon l'emplacement de ce fichier

// 🔐 Rediriger si l'utilisateur n'est pas connecté
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url());
    exit;
}

$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_roles = $current_user->roles;

// 🔄 Récupérer les règles de statut par rôle
$rules = get_option('woo_order_filter_rules', []);
$allowed_statuses = [];

foreach ($user_roles as $role) {
    foreach ($rules as $defined_role => $statuses) {
        if (strtolower($defined_role) === strtolower($role)) {
            $allowed_statuses = array_merge($allowed_statuses, $statuses);
        }
    }
}

$allowed_statuses = array_unique($allowed_statuses);


if (empty($allowed_statuses)) {
    echo "<h3>Vous n’avez pas accès aux commandes.</h3>";
    exit;
}

// 🧾 Récupération des commandes du user connecté avec statuts filtrés
$orders = wc_get_orders([
    'customer_id' => $user_id,
    'status'      => $allowed_statuses,
    'limit'       => 20,
    'orderby'     => 'date',
    'order'       => 'DESC'
]);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Mes commandes</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 10px; border: 1px solid #ccc; text-align: left; }
        th { background-color: #f0f0f0; }
    </style>
</head>
<body>

<h1>Mes commandes WooCommerce</h1>

<?php if (empty($orders)) : ?>
    <p>Aucune commande à afficher.</p>
<?php else : ?>
    <table>
        <tr>
            <th>ID</th>
            <th>Date</th>
            <th>Statut</th>
            <th>Total</th>
        </tr>
        <?php foreach ($orders as $order): ?>
            <tr>
                <td>#<?php echo $order->get_id(); ?></td>
                <td><?php echo $order->get_date_created()->date_i18n(); ?></td>
                <td><?php echo wc_get_order_status_name($order->get_status()); ?></td>
                <td><?php echo $order->get_formatted_order_total(); ?></td>
            </tr>
        <?php endforeach; ?>
    </table>
<?php endif; ?>

</body>
</html>