<?php
require_once('../wp-load.php'); // Ajuste ce chemin selon l'emplacement de ce fichier

// 🔄 Rediriger si l'utilisateur est déjà connecté
if (is_user_logged_in()) {
    wp_redirect('index.php');
    exit;
}

// Variables pour les messages
$error_message = '';
$success_message = '';

// 🔐 Traitement de la connexion
if (isset($_POST['login_submit'])) {
    // Vérifier le nonce pour la sécurité
    if (wp_verify_nonce($_POST['login_nonce'], 'user_login')) {
        $username = sanitize_text_field($_POST['username']);
        $password = $_POST['password'];
        $remember = isset($_POST['remember']) ? true : false;
        
        if (empty($username) || empty($password)) {
            $error_message = 'Veuillez remplir tous les champs.';
        } else {
            // Tentative de connexion
            $credentials = array(
                'user_login'    => $username,
                'user_password' => $password,
                'remember'      => $remember
            );
            
            $user = wp_signon($credentials, false);
            
            if (is_wp_error($user)) {
                $error_message = 'Nom d\'utilisateur ou mot de passe incorrect.';
            } else {
                // Connexion réussie - vérifier les permissions
                $user_roles = $user->roles;
                
                // Inclure la classe du plugin si elle n'est pas déjà chargée
                if (!class_exists('WooOrderStatusRoleFilter')) {
                    require_once('woo-orders.php');
                }
                
                $permissions = WooOrderStatusRoleFilter::get_user_order_permissions($user_roles);
                
                if (empty($permissions['view'])) {
                    // L'utilisateur n'a pas de permissions pour voir les commandes
                    wp_logout();
                    $error_message = 'Votre compte n\'a pas accès à cette section. Contactez l\'administrateur.';
                } else {
                    // Redirection vers la page des commandes
                    wp_redirect('index.php');
                    exit;
                }
            }
        }
    } else {
        $error_message = 'Erreur de sécurité. Veuillez réessayer.';
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Gestion des commandes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .login-header {
            background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .login-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .login-header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .login-form {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .form-group input[type="text"],
        .form-group input[type="password"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }
        
        .form-group input[type="text"]:focus,
        .form-group input[type="password"]:focus {
            outline: none;
            border-color: #0073aa;
            background-color: white;
            box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .checkbox-group input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        
        .checkbox-group label {
            margin-bottom: 0;
            font-weight: normal;
            cursor: pointer;
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 115, 170, 0.3);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .message {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: center;
        }
        
        .message.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .message.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .login-footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .login-footer p {
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 10px;
        }
        
        .back-link {
            color: #0073aa;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        @media (max-width: 480px) {
            .login-container {
                margin: 10px;
            }
            
            .login-header {
                padding: 20px 15px;
            }
            
            .login-form {
                padding: 20px;
            }
        }
    </style>
</head>
<body>

<div class="login-container">
    <div class="login-header">
        <div class="icon">🛒</div>
        <h1>Connexion</h1>
        <p>Accès à la gestion des commandes</p>
    </div>
    
    <div class="login-form">
        <?php if (!empty($error_message)): ?>
            <div class="message error">
                ❌ <?php echo esc_html($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success_message)): ?>
            <div class="message success">
                ✅ <?php echo esc_html($success_message); ?>
            </div>
        <?php endif; ?>
        
        <form method="post" action="">
            <?php wp_nonce_field('user_login', 'login_nonce'); ?>
            
            <div class="form-group">
                <label for="username">👤 Nom d'utilisateur ou Email</label>
                <input type="text" 
                       id="username" 
                       name="username" 
                       value="<?php echo isset($_POST['username']) ? esc_attr($_POST['username']) : ''; ?>"
                       required 
                       autocomplete="username"
                       placeholder="Entrez votre nom d'utilisateur">
            </div>
            
            <div class="form-group">
                <label for="password">🔒 Mot de passe</label>
                <input type="password" 
                       id="password" 
                       name="password" 
                       required 
                       autocomplete="current-password"
                       placeholder="Entrez votre mot de passe">
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="remember" name="remember" value="1">
                <label for="remember">Se souvenir de moi</label>
            </div>
            
            <button type="submit" name="login_submit" class="login-btn">
                Se connecter
            </button>
        </form>
    </div>
    
    <div class="login-footer">
        <p>🔐 Connexion sécurisée</p>
        <a href="<?php echo home_url(); ?>" class="back-link">← Retour au site</a>
    </div>
</div>

<script>
// Auto-focus sur le premier champ
document.addEventListener('DOMContentLoaded', function() {
    const usernameField = document.getElementById('username');
    if (usernameField && !usernameField.value) {
        usernameField.focus();
    }
});

// Animation des champs au focus
document.querySelectorAll('input[type="text"], input[type="password"]').forEach(input => {
    input.addEventListener('focus', function() {
        this.parentElement.style.transform = 'scale(1.02)';
        this.parentElement.style.transition = 'transform 0.2s ease';
    });
    
    input.addEventListener('blur', function() {
        this.parentElement.style.transform = 'scale(1)';
    });
});
</script>

</body>
</html>
