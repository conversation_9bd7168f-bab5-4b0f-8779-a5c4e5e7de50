/*!
 * Dialogs Manager v4.9.3
 * https://github.com/kobizz/dialogs-manager
 *
 * Copyright <PERSON><PERSON>
 * Released under the MIT license
 * https://github.com/kobizz/dialogs-manager/blob/master/LICENSE.txt
 */
!function(t,e){"use strict";var n={widgetsTypes:{},createWidgetType:function(e,i,o){o||(o=this.Widget);var s=function(){o.apply(this,arguments)},r=s.prototype=new o(e);return r.types=r.types.concat([e]),t.extend(r,i),r.constructor=s,s.extend=function(t,e){return n.createWidgetType(t,e,s)},s},addWidgetType:function(t,e,n){return e&&e.prototype instanceof this.Widget?this.widgetsTypes[t]=e:this.widgetsTypes[t]=this.createWidgetType(t,e,n)},getWidgetType:function(t){return this.widgetsTypes[t]}};n.Instance=function(){var e=this,i={},o={},s=function(e){t.extend(o,{classPrefix:"dialog",effects:{show:"fadeIn",hide:"fadeOut"}},e)};this.createWidget=function(t,i){var o=new(n.getWidgetType(t))(t);return i=i||{},o.init(e,i),o},this.getSettings=function(t){return t?o[t]:Object.create(o)},this.maybeLoadAssets=async function(){if(!!window.elementorFrontend?.utils?.assetsLoader)try{await elementorFrontend.utils.assetsLoader.load("style","dialog")}catch(t){console.error("Failed to load assets:",t)}},this.init=function(n){return this.maybeLoadAssets(),s(n),i.body=t("body"),e},e.init()},n.Widget=function(e){var i=this,o={},s={},r={},a=0,c=["refreshPosition"],u=function(t,e){var n=o.effects[t],i=r.widget;if("function"==typeof n)n.apply(i,e);else{if(!i[n])throw"Reference Error: The effect "+n+" not found";i[n].apply(i,e)}},d=function(e){if(!g(e)){if(o.hide.onClick){if(t(e.target).closest(o.selectors.preventClose).length)return}else if(e.target!==this)return;i.hide()}},l=function(e){g(e)||t(e.target).closest(r.widget).length||function(e){return!!o.hide.ignore&&!!t(e.target).closest(o.hide.ignore).length}(e)||i.hide()},f=function(){t.each(o,(function(t){var e=t.match(/^on([A-Z].*)/);e&&(e=e[1].charAt(0).toLowerCase()+e[1].slice(1),i.on(e,this))}))},g=function(t){return"click"===t.type&&2===t.button},h=function(t){27===t.which&&i.hide()},p=function(){var t=[r.window];r.iframe&&t.push(jQuery(r.iframe[0].contentWindow)),t.forEach((function(t){o.hide.onEscKeyPress&&t.off("keyup",h),o.hide.onOutsideClick&&t[0].removeEventListener("click",l,!0),o.hide.onOutsideContextMenu&&t[0].removeEventListener("contextmenu",l,!0),o.position.autoRefresh&&t.off("resize",i.refreshPosition)})),(o.hide.onClick||o.hide.onBackgroundClick)&&r.widget.off("click",d)};this.addElement=function(e,n,i){var s=r[e]=t(n||"<div>"),a=function(t){return t.replace(/([a-z])([A-Z])/g,(function(){return arguments[1]+"-"+arguments[2].toLowerCase()}))}(e);return i=i?i+" ":"",i+=o.classes.globalPrefix+"-"+a,i+=" "+o.classes.prefix+"-"+a,s.addClass(i),s},this.destroy=function(){return p(),r.widget.remove(),i.trigger("destroy"),i},this.getElements=function(t){return t?r[t]:r},this.getSettings=function(t){var e=Object.create(o);return t?e[t]:e},this.hide=function(){if(i.isVisible())return clearTimeout(a),u("hide",arguments),p(),o.preventScroll&&i.getElements("body").removeClass(o.classes.preventScroll),i.trigger("hide"),i},this.init=function(s,a){if(!(s instanceof n.Instance))throw"The "+i.widgetName+" must to be initialized from an instance of DialogsManager.Instance";var u;return u=c.concat(i.getClosureMethods()),t.each(u,(function(){var t=i[this];i[this]=function(){t.apply(i,arguments)}})),i.trigger("init",a),function(n,s){var r=t.extend(!0,{},n.getSettings());o={headerMessage:"",message:"",effects:r.effects,classes:{globalPrefix:r.classPrefix,prefix:r.classPrefix+"-"+e,preventScroll:r.classPrefix+"-prevent-scroll"},selectors:{preventClose:"."+r.classPrefix+"-prevent-close"},container:"body",preventScroll:!1,iframe:null,closeButton:!1,closeButtonOptions:{iconClass:r.classPrefix+"-close-button-icon",attributes:{role:"button",tabindex:0,"aria-label":"Close",href:"#"},iconElement:"<i>"},position:{element:"widget",my:"center",at:"center",enable:!0,autoRefresh:!1},hide:{auto:!1,autoDelay:5e3,onClick:!1,onOutsideClick:!0,onOutsideContextMenu:!1,onBackgroundClick:!0,onEscKeyPress:!0,ignore:""}},t.extend(!0,o,i.getDefaultSettings(),s),f()}(s,a),function(){if(i.addElement("widget"),i.addElement("header"),i.addElement("message"),i.addElement("window",window),i.addElement("body",document.body),i.addElement("container",o.container),o.iframe&&i.addElement("iframe",o.iframe),o.closeButton){o.closeButtonClass&&(o.closeButtonOptions.iconClass=o.closeButtonClass);const e=t("<a>",o.closeButtonOptions.attributes),n=t(o.closeButtonOptions.iconElement).addClass(o.closeButtonOptions.iconClass);e.append(n),i.addElement("closeButton",e)}var e=i.getSettings("id");e&&i.setID(e);var n=[];t.each(i.types,(function(){n.push(o.classes.globalPrefix+"-type-"+this)})),n.push(i.getSettings("className")),r.widget.addClass(n.join(" ")).attr({"aria-modal":!0,role:"document",tabindex:0})}(),i.buildWidget(),i.attachEvents(),i.trigger("ready"),i},this.isVisible=function(){return r.widget.is(":visible")},this.on=function(e,n){return"object"==typeof e?(t.each(e,(function(t){i.on(t,this)})),i):(e.split(" ").forEach((function(t){s[t]||(s[t]=[]),s[t].push(n)})),i)},this.off=function(t,e){if(!s[t])return i;if(!e)return delete s[t],i;var n=s[t].indexOf(e);return-1!==n&&s[t].splice(n,1),i},this.refreshPosition=function(){if(o.position.enable){var e=t.extend({},o.position);r[e.of]&&(e.of=r[e.of]),e.of||(e.of=window),o.iframe&&function(t){if(t.my){var e=/([+-]\d+)?$/,n=r.iframe.offset(),i=r.iframe[0].contentWindow,o=t.my.split(" "),s=[];1===o.length&&(/left|right/.test(o[0])?o.push("center"):o.unshift("center")),o.forEach((function(t,o){var r=t.replace(e,(function(t){return t=+t||0,(t+=o?n.top-i.scrollY:n.left-i.scrollX)>=0&&(t="+"+t),t}));s.push(r)})),t.my=s.join(" ")}}(e),r[e.element].position(e)}},this.setID=function(t){return r.widget.attr("id",t),i},this.setHeaderMessage=function(t){return i.getElements("header").html(t),i},this.setMessage=function(t){return r.message.html(t),i},this.setSettings=function(e,n){return jQuery.isPlainObject(n)?t.extend(!0,o[e],n):o[e]=n,i},this.show=function(){var t;return clearTimeout(a),r.widget.appendTo(r.container).hide(),u("show",arguments),i.refreshPosition(),o.hide.auto&&(a=setTimeout(i.hide,o.hide.autoDelay)),t=[r.window],r.iframe&&t.push(jQuery(r.iframe[0].contentWindow)),t.forEach((function(t){o.hide.onEscKeyPress&&t.on("keyup",h),o.hide.onOutsideClick&&t[0].addEventListener("click",l,!0),o.hide.onOutsideContextMenu&&t[0].addEventListener("contextmenu",l,!0),o.position.autoRefresh&&t.on("resize",i.refreshPosition)})),(o.hide.onClick||o.hide.onBackgroundClick)&&r.widget.on("click",d),o.preventScroll&&i.getElements("body").addClass(o.classes.preventScroll),i.trigger("show"),i},this.trigger=function(e,n){var o="on"+e[0].toUpperCase()+e.slice(1);i[o]&&i[o](n);var r=s[e];if(r)return t.each(r,(function(t,e){e.call(i,n)})),i}},n.Widget.prototype.types=[],n.Widget.prototype.buildWidget=function(){var t=this.getElements(),e=this.getSettings();t.widget.append(t.header,t.message),this.setHeaderMessage(e.headerMessage),this.setMessage(e.message),this.getSettings("closeButton")&&t.widget.prepend(t.closeButton)},n.Widget.prototype.attachEvents=function(){var t=this;t.getSettings("closeButton")&&t.getElements("closeButton").on("click",(function(e){e.preventDefault(),t.hide()}))},n.Widget.prototype.getDefaultSettings=function(){return{}},n.Widget.prototype.getClosureMethods=function(){return[]},n.Widget.prototype.onHide=function(){},n.Widget.prototype.onShow=function(){},n.Widget.prototype.onInit=function(){},n.Widget.prototype.onReady=function(){},n.widgetsTypes.simple=n.Widget,n.addWidgetType("buttons",{activeKeyUp:function(t){9===t.which&&t.preventDefault(),this.hotKeys[t.which]&&this.hotKeys[t.which](this)},activeKeyDown:function(t){if(this.focusedButton){if(9===t.which){t.preventDefault();var e,n=this.focusedButton.index();t.shiftKey?(e=n-1)<0&&(e=this.buttons.length-1):(e=n+1)>=this.buttons.length&&(e=0),this.focusedButton=this.buttons[e].trigger("focus")}}},addButton:function(e){var n=this,i=n.getSettings(),o=jQuery.extend(i.button,e),s=e.classes?e.classes+" ":"";s+=i.classes.globalPrefix+"-button";var r=n.addElement(e.name,t("<"+o.tag+">").html(e.text),s);n.buttons.push(r);var a=function(){i.hide.onButtonClick&&n.hide(),"function"==typeof e.callback&&e.callback.call(this,n)};return r.on("click",a),e.hotKey&&(this.hotKeys[e.hotKey]=a),this.getElements("buttonsWrapper").append(r),e.focus&&(this.focusedButton=r),n},bindHotKeys:function(){this.getElements("window").on({keyup:this.activeKeyUp,keydown:this.activeKeyDown})},buildWidget:function(){n.Widget.prototype.buildWidget.apply(this,arguments);var t=this.addElement("buttonsWrapper");this.getElements("widget").append(t)},getClosureMethods:function(){return["activeKeyUp","activeKeyDown"]},getDefaultSettings:function(){return{hide:{onButtonClick:!0},button:{tag:"button"}}},onHide:function(){this.unbindHotKeys()},onInit:function(){this.buttons=[],this.hotKeys={},this.focusedButton=null},onShow:function(){this.bindHotKeys(),this.focusedButton||(this.focusedButton=this.buttons[0]),this.focusedButton&&this.focusedButton.trigger("focus")},unbindHotKeys:function(){this.getElements("window").off({keyup:this.activeKeyUp,keydown:this.activeKeyDown})}}),n.addWidgetType("lightbox",n.getWidgetType("buttons").extend("lightbox",{getDefaultSettings:function(){var e=n.getWidgetType("buttons").prototype.getDefaultSettings.apply(this,arguments);return t.extend(!0,e,{contentWidth:"auto",contentHeight:"auto",position:{element:"widgetContent",of:"widget",autoRefresh:!0}})},buildWidget:function(){n.getWidgetType("buttons").prototype.buildWidget.apply(this,arguments);var t=this.addElement("widgetContent"),e=this.getElements();t.append(e.header,e.message,e.buttonsWrapper),e.widget.html(t),e.closeButton&&t.prepend(e.closeButton)},onReady:function(){var t=this.getElements(),e=this.getSettings();"auto"!==e.contentWidth&&t.message.width(e.contentWidth),"auto"!==e.contentHeight&&t.message.height(e.contentHeight)}})),n.addWidgetType("confirm",n.getWidgetType("lightbox").extend("confirm",{onReady:function(){n.getWidgetType("lightbox").prototype.onReady.apply(this,arguments);var t=this.getSettings("strings"),e="cancel"===this.getSettings("defaultOption");this.addButton({name:"cancel",text:t.cancel,callback:function(t){t.trigger("cancel")},focus:e}),this.addButton({name:"ok",text:t.confirm,callback:function(t){t.trigger("confirm")},focus:!e})},getDefaultSettings:function(){var t=n.getWidgetType("lightbox").prototype.getDefaultSettings.apply(this,arguments);return t.strings={confirm:"OK",cancel:"Cancel"},t.defaultOption="cancel",t}})),n.addWidgetType("alert",n.getWidgetType("lightbox").extend("alert",{onReady:function(){n.getWidgetType("lightbox").prototype.onReady.apply(this,arguments);var t=this.getSettings("strings");this.addButton({name:"ok",text:t.confirm,callback:function(t){t.trigger("confirm")}})},getDefaultSettings:function(){var t=n.getWidgetType("lightbox").prototype.getDefaultSettings.apply(this,arguments);return t.strings={confirm:"OK"},t}})),e.DialogsManager=n}("undefined"!=typeof jQuery?jQuery:"function"==typeof require&&require("jquery"),"undefined"!=typeof module&&void 0!==module.exports?module.exports:window);
