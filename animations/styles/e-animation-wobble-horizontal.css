@keyframes elementor-animation-wobble-horizontal {
	16.65% {
		transform: translateX(8px);
	}
	33.3% {
		transform: translateX(-6px);
	}
	49.95% {
		transform: translateX(4px);
	}
	66.6% {
		transform: translateX(-2px);
	}
	83.25% {
		transform: translateX(1px);
	}
	100% {
		transform: translateX(0);
	}
}
.elementor-animation-wobble-horizontal:active,
.elementor-animation-wobble-horizontal:focus,
.elementor-animation-wobble-horizontal:hover {
	animation-name: elementor-animation-wobble-horizontal;
	animation-duration: 1s;
	animation-timing-function: ease-in-out;
	animation-iteration-count: 1;
}
