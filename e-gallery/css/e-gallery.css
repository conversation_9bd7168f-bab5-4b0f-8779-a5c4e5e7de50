/*! E-Gallery v1.2.0 by <PERSON>ementor */
.e-gallery-container {
  position: relative;
  display: flex;
  flex-wrap: wrap; }
  .e-gallery-container:not(.e-gallery-grid) {
    transition: padding-bottom var(--animation-duration); }

.e-gallery-item {
  position: relative;
  flex-grow: 0;
  flex-shrink: 0;
  transition-property: all;
  transition-duration: var(--animation-duration); }
  .e-gallery-item:not(:hover) .e-gallery-overlay {
    display: none; }
  .e-gallery-item.e-gallery-item--hidden {
    transform: scale3d(0, 0, 0);
    opacity: 0; }

.e-gallery-image {
  background-position: center center;
  background-size: cover;
  width: 100%;
  transform-origin: center top;
  transition: var(--animation-duration); }

.e-gallery-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  flex-wrap: wrap;
  flex-direction: column;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5); }
  .e-gallery-overlay__title {
    font-size: 24px; }

.e-gallery-grid:not(.e-gallery--animated) {
  display: grid;
  grid-gap: var(--vgap) var(--hgap);
  grid-template-columns: repeat(var(--columns), 1fr); }
  .e-gallery-grid:not(.e-gallery--animated) .e-gallery-item--hidden {
    position: absolute; }

.e-gallery-grid.e-gallery--animated {
  padding-bottom: var(--container-aspect-ratio); }
  .e-gallery-grid.e-gallery--animated .e-gallery-item {
    --item-width: calc((100% - ((var(--columns) - 1) * var(--hgap))) / var(--columns));
    position: absolute;
    top: calc(((100% / var(--rows)) + (var(--vgap) / var(--rows))) * var(--row));
    width: var(--item-width); }

.e-gallery-grid .e-gallery-image {
  padding-bottom: var(--aspect-ratio); }

.e-gallery-justified {
  padding-bottom: calc(var(--container-aspect-ratio) * 100%); }
  .e-gallery-justified .e-gallery-item {
    position: absolute;
    width: calc(var(--item-width) * (100% - var(--hgap) * var(--gap-count)));
    height: var(--item-height);
    top: calc(var(--item-top) + (var(--row) * var(--vgap))); }
  .e-gallery-justified .e-gallery-image {
    height: 100%; }

.e-gallery-masonry {
  height: 0;
  margin-bottom: calc(var(--highest-column-gap-count) * var(--vgap)); }
  .e-gallery-masonry .e-gallery-item {
    position: absolute;
    width: calc(100% / var(--columns) - (var(--hgap) * (var(--columns) - 1) / var(--columns)));
    top: calc(var(--percent-height) + (var(--items-in-column) * var(--vgap))); }
  .e-gallery-masonry .e-gallery-image {
    padding-bottom: var(--item-height); }

.e-gallery--ltr.e-gallery-grid.e-gallery--animated .e-gallery-item {
  left: calc(var(--item-width) * var(--column) + (var(--hgap) * var(--column))); }

.e-gallery--ltr.e-gallery-justified .e-gallery-item {
  left: calc(var(--item-start) * (100% - var(--hgap) * var(--gap-count)) + var(--hgap) * var(--item-row-index)); }

.e-gallery--ltr.e-gallery-masonry .e-gallery-item {
  left: calc((100% / var(--columns) - (var(--hgap) * (var(--columns) - 1) / var(--columns))) * var(--column) + (var(--hgap) * var(--column))); }

.e-gallery--rtl.e-gallery-grid.e-gallery--animated .e-gallery-item {
  right: calc(var(--item-width) * var(--column) + (var(--hgap) * var(--column))); }

.e-gallery--rtl.e-gallery-justified .e-gallery-item {
  right: calc(var(--item-start) * (100% - var(--hgap) * var(--gap-count)) + var(--hgap) * var(--item-row-index)); }

.e-gallery--rtl.e-gallery-masonry .e-gallery-item {
  right: calc((100% / var(--columns) - (var(--hgap) * (var(--columns) - 1) / var(--columns))) * var(--column) + (var(--hgap) * var(--column))); }

.e-gallery--lazyload .e-gallery-image:not(.e-gallery-image-loaded) {
  filter: opacity(0);
  transform: scale(0.5); }


/*# sourceMappingURL=e-gallery.css.map*/